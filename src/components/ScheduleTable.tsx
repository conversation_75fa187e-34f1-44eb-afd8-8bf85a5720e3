import { Card } from '@/components/ui/card';

interface ScheduleData {
  [key: string]: {
    [key: string]: string;
  };
}

const scheduleData: ScheduleData = {
  '第1节': {
    '星期一': '数学',
    '星期二': '英语',
    '星期三': '语文',
    '星期四': '数学',
    '星期五': '语文'
  },
  '第2节': {
    '星期一': '语文',
    '星期二': '体育与健康',
    '星期三': '数学',
    '星期四': '音乐',
    '星期五': '语文'
  },
  '大课间': {
    '星期一': '阳光体育大课间',
    '星期二': '阳光体育大课间',
    '星期三': '阳光体育大课间',
    '星期四': '阳光体育大课间',
    '星期五': '阳光体育大课间'
  },
  '第3节': {
    '星期一': '语文',
    '星期二': '数学',
    '星期三': '英语',
    '星期四': '体育与健康',
    '星期五': '科学'
  },
  '第4节': {
    '星期一': '美术',
    '星期二': '语文',
    '星期三': '体育与健康',
    '星期四': '语文',
    '星期五': '体育与健康'
  },
  '午休': {
    '星期一': '午休',
    '星期二': '午休',
    '星期三': '午休',
    '星期四': '午休',
    '星期五': '午休'
  },
  '第5节': {
    '星期一': '综合实践',
    '星期二': '德法',
    '星期三': '地（队会/心理）',
    '星期四': '美术',
    '星期五': '信息科技'
  },
  '第6节': {
    '星期一': '体育与健康',
    '星期二': '科学',
    '星期三': '德法',
    '星期四': '劳动',
    '星期五': '音乐'
  },
  '第7节': {
    '星期一': '',
    '星期二': '',
    '星期三': '',
    '星期四': '',
    '星期五': ''
  }
};

const timeSlots = ['第1节', '第2节', '大课间', '第3节', '第4节', '午休', '第5节', '第6节', '第7节'];
const weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五'];

export const ScheduleTable = () => {
  const isBreakTime = (slot: string) => {
    return slot === '大课间' || slot === '午休';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-8xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
          课程表
        </h1>
        <p className="text-5xl text-muted-foreground">2025-2026学年度第一学期</p>
        <p className="text-4xl text-muted-foreground">南京师范大学附属中学新城小学</p>
      </div>


      {/* Schedule Table */}
      <Card className="p-6 shadow-schedule border-border/50 bg-card">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="bg-schedule-header text-left p-6 border border-border font-semibold text-foreground rounded-tl-lg text-4xl">
                  时间
                </th>
                {weekdays.map((day, index) => (
                  <th 
                    key={day} 
                    className={`bg-schedule-header text-center p-6 border border-border font-semibold text-foreground text-4xl ${
                      index === weekdays.length - 1 ? 'rounded-tr-lg' : ''
                    }`}
                  >
                    {day}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {timeSlots.map((slot, rowIndex) => {
                // Skip empty rows
                if (slot === '第7节' && !scheduleData[slot]['星期一']) {
                  return null;
                }
                
                const isBreak = isBreakTime(slot);
                return (
                  <tr key={slot} className="hover:bg-accent/50 transition-colors duration-150">
                    <td className={`p-6 border border-border font-medium text-4xl ${
                      isBreak ? 'bg-schedule-break text-foreground' : 'bg-schedule-cell'
                    }`}>
                      {slot}
                    </td>
                    {weekdays.map((day, colIndex) => (
                      <td 
                        key={day} 
                        className={`p-6 border border-border text-center text-4xl ${
                          isBreak ? 'bg-schedule-break' : 'bg-schedule-cell'
                        } ${
                          isBreak && scheduleData[slot][day] === '阳光体育大课间' 
                            ? 'text-academic-blue font-medium' 
                            : isBreak && scheduleData[slot][day] === '午休'
                            ? 'text-muted-foreground font-medium'
                            : 'text-foreground'
                        }`}
                      >
                        {scheduleData[slot][day] || ''}
                      </td>
                    ))}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </Card>

    </div>
  );
};